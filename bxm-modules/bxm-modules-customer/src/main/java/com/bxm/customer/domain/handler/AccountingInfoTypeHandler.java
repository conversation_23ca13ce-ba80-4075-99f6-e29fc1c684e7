package com.bxm.customer.domain.handler;

import com.baomidou.mybatisplus.extension.handlers.AbstractJsonTypeHandler;
import com.bxm.customer.domain.vo.valueAdded.AccountingInfoVO;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONException;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;

import lombok.extern.slf4j.Slf4j;

/**
 * AccountingInfoVO <-> JSON字符串 的MyBatis-Plus TypeHandler
 *
 * 继承 AbstractJsonTypeHandler 是MP提供的一种更便捷的方式来处理JSON
 * 使用FastJSON进行JSON序列化和反序列化处理
 */
@Slf4j
@MappedTypes(AccountingInfoVO.class)
@MappedJdbcTypes(JdbcType.VARCHAR)
public class AccountingInfoTypeHandler extends AbstractJsonTypeHandler<AccountingInfoVO> {

    @Override
    protected AccountingInfoVO parse(String json) {
        try {
            if (json == null || json.trim().isEmpty()) {
                log.warn("尝试解析空的JSON字符串为AccountingInfoVO");
                return null;
            }

            AccountingInfoVO result = JSON.parseObject(json, AccountingInfoVO.class);

            return result;
        } catch (JSONException e) {
            log.error("JSON反序列化为AccountingInfoVO失败: {}", json, e);
            throw new RuntimeException("无法将JSON反序列化为AccountingInfoVO: " + e.getMessage(), e);
        }
    }

    @Override
    protected String toJson(AccountingInfoVO obj) {
        try {
            if (obj == null) {
                return null;
            }

            // 在序列化前验证业务逻辑
            if (!obj.isValid()) {
                String errorMsg = obj.getValidationMessage();
                log.error("尝试序列化无效的AccountingInfoVO: {}", errorMsg);
                throw new RuntimeException("账务类型信息验证失败，无法序列化: " + errorMsg);
            }

            return JSON.toJSONString(obj);
        } catch (JSONException e) {
            log.error("AccountingInfoVO序列化为JSON失败: {}", obj, e);
            throw new RuntimeException("无法将AccountingInfoVO序列化为JSON: " + e.getMessage(), e);
        }
    }
}
